import pytest
from unittest.mock import AsyncMock

from app.integrations.types import IntegrationSource
from app.workspace.schemas.integration import IntegrationInfo, IntegrationsListResponse
from app.workspace.types import IntegrationType


@pytest.mark.anyio
async def test_get_integrations_endpoint(client, mock_user_env, mocker):
    """Test the GET /integrations endpoint"""
    
    # Mock the integration list service
    mock_service = mocker.Mock()
    mock_service.get_integrations_list = AsyncMock()
    
    # Create mock response data
    active_integrations = [
        IntegrationInfo(
            source=IntegrationSource.SALESFORCE,
            integration_type=IntegrationType.CRM,
            name="Salesforce",
            description="Connect your Salesforce CRM to sync leads, contacts, and opportunities",
            is_active=True,
            config_id="123e4567-e89b-12d3-a456-426614174000",
        ),
        IntegrationInfo(
            source=IntegrationSource.SLACK,
            integration_type=IntegrationType.MESSAGING,
            name="Slack",
            description="Connect your Slack workspace for team notifications",
            is_active=True,
            config_id="123e4567-e89b-12d3-a456-426614174001",
        ),
    ]
    
    available_integrations = [
        IntegrationInfo(
            source=IntegrationSource.HUBSPOT,
            integration_type=IntegrationType.CRM,
            name="HubSpot",
            description="Sync your HubSpot contacts and deals with Pearl",
            is_active=False,
            config_id=None,
        ),
        IntegrationInfo(
            source=IntegrationSource.GOOGLE_CALENDAR,
            integration_type=IntegrationType.CALENDAR,
            name="Google Calendar",
            description="Sync your Google Calendar events and meetings",
            is_active=False,
            config_id=None,
        ),
    ]
    
    mock_response = IntegrationsListResponse(
        active_integrations=active_integrations,
        available_integrations=available_integrations,
    )
    
    mock_service.get_integrations_list.return_value = mock_response
    
    # Mock the dependency
    mocker.patch(
        "app.workspace.routers.integrations.IntegrationListServiceDep",
        return_value=mock_service,
    )
    
    # Make the request
    response = client.get("/integrations")
    
    # Verify the response
    assert response.status_code == 200
    data = response.json()
    
    assert "active_integrations" in data
    assert "available_integrations" in data
    
    assert len(data["active_integrations"]) == 2
    assert len(data["available_integrations"]) == 2
    
    # Check active integrations
    active = data["active_integrations"]
    assert active[0]["source"] == "salesforce"
    assert active[0]["is_active"] is True
    assert active[0]["config_id"] is not None
    
    assert active[1]["source"] == "slack"
    assert active[1]["is_active"] is True
    assert active[1]["config_id"] is not None
    
    # Check available integrations
    available = data["available_integrations"]
    assert available[0]["source"] == "hubspot"
    assert available[0]["is_active"] is False
    assert available[0]["config_id"] is None
    
    assert available[1]["source"] == "google_calendar"
    assert available[1]["is_active"] is False
    assert available[1]["config_id"] is None
