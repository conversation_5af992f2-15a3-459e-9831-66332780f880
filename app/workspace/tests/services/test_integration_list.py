import pytest
from unittest.mock import AsyncMock
from uuid import uuid4

from app.integrations.types import IntegrationSource
from app.workspace.models import IntegrationConfig
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_list import IntegrationListService
from app.workspace.types import EnvironmentType, IntegrationType


@pytest.fixture
def mock_integration_config_service(mocker):
    return mocker.Mock()


@pytest.fixture
def integration_list_service(mock_integration_config_service):
    return IntegrationListService(mock_integration_config_service)


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        organization_id=uuid4(),
        environment_id=uuid4(),
        type=EnvironmentType.PROD,
    )


@pytest.mark.anyio
async def test_get_integrations_list_with_active_integrations(
    integration_list_service, mock_integration_config_service, mock_environment, mocker
):
    """Test getting integrations list when some integrations are active"""
    
    # Mock configured integrations
    config1_id = uuid4()
    config2_id = uuid4()
    
    configured_integrations = [
        mocker.Mock(
            spec=IntegrationConfig,
            id=config1_id,
            source=IntegrationSource.SALESFORCE,
            integration_type=IntegrationType.CRM,
        ),
        mocker.Mock(
            spec=IntegrationConfig,
            id=config2_id,
            source=IntegrationSource.SLACK,
            integration_type=IntegrationType.MESSAGING,
        ),
    ]
    
    mock_integration_config_service.get_integration_configs = AsyncMock(
        return_value=configured_integrations
    )
    
    # Call the service
    result = await integration_list_service.get_integrations_list(mock_environment)
    
    # Verify the call was made
    mock_integration_config_service.get_integration_configs.assert_called_once_with(
        mock_environment
    )
    
    # Check the result structure
    assert hasattr(result, "active_integrations")
    assert hasattr(result, "available_integrations")
    
    # Check active integrations
    active_sources = {integration.source for integration in result.active_integrations}
    assert IntegrationSource.SALESFORCE in active_sources
    assert IntegrationSource.SLACK in active_sources
    
    # Check that active integrations have config_ids
    for integration in result.active_integrations:
        assert integration.is_active is True
        assert integration.config_id is not None
        if integration.source == IntegrationSource.SALESFORCE:
            assert integration.config_id == config1_id
        elif integration.source == IntegrationSource.SLACK:
            assert integration.config_id == config2_id
    
    # Check available integrations (should include HubSpot, Teams, etc.)
    available_sources = {integration.source for integration in result.available_integrations}
    assert IntegrationSource.HUBSPOT in available_sources
    assert IntegrationSource.TEAMS in available_sources
    assert IntegrationSource.GOOGLE_CALENDAR in available_sources
    assert IntegrationSource.OUTLOOK_CALENDAR in available_sources
    assert IntegrationSource.GCS in available_sources
    
    # Check that available integrations don't have config_ids
    for integration in result.available_integrations:
        assert integration.is_active is False
        assert integration.config_id is None


@pytest.mark.anyio
async def test_get_integrations_list_no_active_integrations(
    integration_list_service, mock_integration_config_service, mock_environment
):
    """Test getting integrations list when no integrations are active"""
    
    # Mock no configured integrations
    mock_integration_config_service.get_integration_configs = AsyncMock(
        return_value=[]
    )
    
    # Call the service
    result = await integration_list_service.get_integrations_list(mock_environment)
    
    # Check that no integrations are active
    assert len(result.active_integrations) == 0
    
    # Check that all integrations are available
    assert len(result.available_integrations) == len(IntegrationSource)
    
    # Verify all available integrations are not active
    for integration in result.available_integrations:
        assert integration.is_active is False
        assert integration.config_id is None


def test_get_integration_metadata(integration_list_service):
    """Test that integration metadata is properly defined"""
    
    metadata = integration_list_service._get_integration_metadata()
    
    # Check that all integration sources have metadata
    for source in IntegrationSource:
        assert source in metadata
        assert "name" in metadata[source]
        assert "description" in metadata[source]
        assert isinstance(metadata[source]["name"], str)
        assert isinstance(metadata[source]["description"], str)
        assert len(metadata[source]["name"]) > 0
        assert len(metadata[source]["description"]) > 0
