from app.integrations.types import IntegrationSource
from app.workspace.schemas import OrgEnvironment
from app.workspace.schemas.integration import IntegrationInfo, IntegrationsListResponse
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.types import SOURCE_TYPE_MAP


class IntegrationListService:
    """Service for listing available and active integrations"""

    def __init__(self, integration_config_service: IntegrationConfigService):
        self.integration_config_service = integration_config_service

    async def get_integrations_list(
        self, environment: OrgEnvironment
    ) -> IntegrationsListResponse:
        """Get list of integrations split between active and available"""
        configured_integrations = (
            await self.integration_config_service.get_integration_configs(environment)
        )

        # Create a set of active integration sources
        active_sources = {config.source for config in configured_integrations}

        # Define integration metadata
        integration_metadata = self._get_integration_metadata()

        active_integrations = []
        available_integrations = []

        for source in IntegrationSource:
            if source not in integration_metadata:
                continue

            metadata = integration_metadata[source]
            integration_type = SOURCE_TYPE_MAP[source]

            # Find the config for this source if it exists
            config = next(
                (
                    config
                    for config in configured_integrations
                    if config.source == source
                ),
                None,
            )

            integration_info = IntegrationInfo(
                source=source,
                integration_type=integration_type,
                name=metadata["name"],
                description=metadata["description"],
                is_active=source in active_sources,
                config_id=config.id if config else None,
            )

            if source in active_sources:
                active_integrations.append(integration_info)
            else:
                available_integrations.append(integration_info)

        return IntegrationsListResponse(
            active_integrations=active_integrations,
            available_integrations=available_integrations,
        )

    def _get_integration_metadata(self) -> dict[IntegrationSource, dict[str, str]]:
        """Get metadata for each integration source"""
        return {
            IntegrationSource.SALESFORCE: {
                "name": "Salesforce",
                "description": "Connect your Salesforce CRM to sync leads, contacts, and opportunities",
            },
            IntegrationSource.HUBSPOT: {
                "name": "HubSpot",
                "description": "Sync your HubSpot contacts and deals with Pearl",
            },
            IntegrationSource.SLACK: {
                "name": "Slack",
                "description": "Connect your Slack workspace for team notifications",
            },
            IntegrationSource.TEAMS: {
                "name": "Microsoft Teams",
                "description": "Connect your Microsoft Teams for team collaboration",
            },
            IntegrationSource.GCS: {
                "name": "Google Cloud Storage",
                "description": "Connect to Google Cloud Storage for file management",
            },
            IntegrationSource.GOOGLE_CALENDAR: {
                "name": "Google Calendar",
                "description": "Sync your Google Calendar events and meetings",
            },
            IntegrationSource.OUTLOOK_CALENDAR: {
                "name": "Outlook Calendar",
                "description": "Connect your Microsoft Outlook calendar",
            },
        }
