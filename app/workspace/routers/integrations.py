from fastapi import APIRouter

from app.workspace.dependencies import IntegrationListServiceDep, UserEnvDep
from app.workspace.schemas.integration import IntegrationsListResponse

router = APIRouter()


@router.get(
    "/integrations",
    response_model=IntegrationsListResponse,
    name="get_integrations",
)
async def get_integrations(
    user_env: UserEnvDep,
    integration_list_service: IntegrationListServiceDep,
):
    return await integration_list_service.get_integrations_list(user_env)
